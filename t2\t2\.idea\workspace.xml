<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="47e270f3-3106-4cc1-a0f5-e64bac03ba03" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/detr/.circleci/config.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/.github/CODE_OF_CONDUCT.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/.github/CONTRIBUTING.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/.github/DETR.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/.github/ISSUE_TEMPLATE/bugs.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/.github/ISSUE_TEMPLATE/questions-help-support.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/.github/ISSUE_TEMPLATE/unexpected-problems-bugs.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/Dockerfile" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/LICENSE" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/d2/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/d2/configs/detr_256_6_6_torchvision.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/d2/configs/detr_segm_256_6_6_torchvision.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/d2/converter.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/d2/detr/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/d2/detr/config.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/d2/detr/dataset_mapper.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/d2/detr/detr.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/d2/train_net.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/datasets/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/datasets/coco.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/datasets/coco_eval.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/datasets/coco_panoptic.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/datasets/panoptic_eval.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/datasets/transforms.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/engine.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/hubconf.py" beforeDir="false" afterPath="$PROJECT_DIR$/detr/hubconf.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/main.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/models/__init__.py" beforeDir="false" afterPath="$PROJECT_DIR$/detr/models/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/models/detr.py" beforeDir="false" afterPath="$PROJECT_DIR$/detr/models/detr.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/models/matcher.py" beforeDir="false" afterPath="$PROJECT_DIR$/detr/models/matcher.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/run_with_submitit.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/test_all.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/tox.ini" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/detr/util/box_ops.py" beforeDir="false" afterPath="$PROJECT_DIR$/detr/util/box_ops.py" afterDir="false" />
    </list>
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/coco.yaml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="238">
              <caret line="14" column="22" selection-start-line="14" selection-end-line="14" selection-end-column="22" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/medical-pills.yaml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="357">
              <caret line="21" selection-start-line="21" selection-end-line="21" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/medicine.yaml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="255">
              <caret line="15" selection-start-line="15" selection-end-line="15" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/engine/trainer.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="660">
              <caret line="606" column="36" selection-start-line="606" selection-start-column="36" selection-end-line="606" selection-end-column="36" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/runs/detect/train16/args.yaml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="51">
              <caret line="3" column="19" selection-start-line="3" selection-start-column="19" selection-end-line="3" selection-end-column="19" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/data/dataset.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="-13692">
              <caret line="13" column="37" selection-start-line="13" selection-start-column="37" selection-end-line="13" selection-end-column="37" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://D:/Program Files/Anaconda3/envs/yolov8/Lib/site-packages/ultralytics/data/utils.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="328">
              <caret line="401" column="30" selection-start-line="401" selection-start-column="30" selection-end-line="401" selection-end-column="30" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://D:/Program Files/Anaconda3/envs/yolov8/Lib/site-packages/ultralytics/utils/checks.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="396">
              <caret line="549" column="31" selection-start-line="549" selection-start-column="31" selection-end-line="549" selection-end-column="31" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/medicine.yaml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="153">
              <caret line="9" selection-start-line="9" selection-end-line="11" selection-end-column="17" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/VOC.yaml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="-102" />
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>meidical-pills.yaml</find>
      <find>medical-pills.yaml</find>
    </findStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/detr" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/ultralytics/ultralytics/engine/trainer.py" />
        <option value="$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/medicine.yaml" />
        <option value="$PROJECT_DIR$/medicine.yaml" />
      </list>
    </option>
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="580" />
    <option name="y" value="200" />
    <option name="width" value="1400" />
    <option name="height" value="1000" />
  </component>
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="t2" type="b2602c69:ProjectViewProjectNode" />
              <item name="t2" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="t2" type="b2602c69:ProjectViewProjectNode" />
              <item name="t2" type="462c0819:PsiDirectoryNode" />
              <item name="datasets" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="t2" type="b2602c69:ProjectViewProjectNode" />
              <item name="t2" type="462c0819:PsiDirectoryNode" />
              <item name="datasets" type="462c0819:PsiDirectoryNode" />
              <item name="medicine" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="t2" type="b2602c69:ProjectViewProjectNode" />
              <item name="t2" type="462c0819:PsiDirectoryNode" />
              <item name="ultralytics" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="t2" type="b2602c69:ProjectViewProjectNode" />
              <item name="t2" type="462c0819:PsiDirectoryNode" />
              <item name="ultralytics" type="462c0819:PsiDirectoryNode" />
              <item name="ultralytics" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="settings.editor.selected.configurable" value="com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="47e270f3-3106-4cc1-a0f5-e64bac03ba03" name="Default Changelist" comment="" />
      <created>1754600830271</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754600830271</updated>
    </task>
    <servers />
  </component>
  <component name="TodoView">
    <todo-panel id="selected-file">
      <is-autoscroll-to-source value="true" />
    </todo-panel>
    <todo-panel id="all">
      <are-packages-shown value="true" />
      <is-autoscroll-to-source value="true" />
    </todo-panel>
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="2576" height="1416" extended-state="6" />
    <layout>
      <window_info id="Favorites" side_tool="true" />
      <window_info content_ui="combo" id="Project" order="0" visible="true" weight="0.14342001" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info anchor="bottom" id="Version Control" weight="0.3299532" />
      <window_info anchor="bottom" id="Python Console" weight="0.3299532" />
      <window_info active="true" anchor="bottom" id="Terminal" sideWeight="0.49960598" visible="true" weight="0.3299532" />
      <window_info anchor="bottom" id="Event Log" sideWeight="0.500394" side_tool="true" weight="0.3299532" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" weight="0.3299532" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
    </layout>
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/detection-full-new.ipynb">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/datasets/drug/data.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="34">
          <caret line="2" column="20" lean-forward="true" selection-start-line="2" selection-start-column="20" selection-end-line="2" selection-end-column="20" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/datasets/medicine/README.roboflow.txt">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/datasets/medicine/README.dataset.txt">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="68">
          <caret line="4" column="18" lean-forward="true" selection-start-line="4" selection-start-column="18" selection-end-line="4" selection-end-column="18" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/coco.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="238">
          <caret line="14" column="22" selection-start-line="14" selection-end-line="14" selection-end-column="22" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/VOC.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-102" />
      </provider>
    </entry>
    <entry file="file://D:/Program Files/Anaconda3/envs/yolov8/Lib/site-packages/ultralytics/utils/checks.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="396">
          <caret line="549" column="31" selection-start-line="549" selection-start-column="31" selection-end-line="549" selection-end-column="31" />
        </state>
      </provider>
    </entry>
    <entry file="file://D:/Program Files/Anaconda3/envs/yolov8/Lib/site-packages/ultralytics/data/utils.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="328">
          <caret line="401" column="30" selection-start-line="401" selection-start-column="30" selection-end-line="401" selection-end-column="30" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/mkdocs.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-3825" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/engine/trainer.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="660">
          <caret line="606" column="36" selection-start-line="606" selection-start-column="36" selection-end-line="606" selection-end-column="36" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/runs/detect/train16/args.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="51">
          <caret line="3" column="19" selection-start-line="3" selection-start-column="19" selection-end-line="3" selection-end-column="19" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/data/dataset.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-13692">
          <caret line="13" column="37" selection-start-line="13" selection-start-column="37" selection-end-line="13" selection-end-column="37" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/default.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1581">
          <caret line="123" column="51" lean-forward="true" selection-start-line="123" selection-start-column="51" selection-end-line="123" selection-end-column="51" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/docs/model_data.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-5073" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/medicine.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="153">
          <caret line="9" selection-start-line="9" selection-end-line="11" selection-end-column="17" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/ultralytics/ultralytics/cfg/datasets/medical-pills.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="357">
          <caret line="21" selection-start-line="21" selection-end-line="21" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/medicine.yaml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="255">
          <caret line="15" selection-start-line="15" selection-end-line="15" />
        </state>
      </provider>
    </entry>
  </component>
</project>